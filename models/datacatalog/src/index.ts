import { Asset, Plugin } from "@hcengineering/platform";
import { Builder } from "@hcengineering/model";
import { TDatacatalogDoc, TDataAssetsSpace, TDomainsSpace, TDataGovernanceSpace } from "./types";
import workbench from "@hcengineering/model-workbench";
import core from "@hcengineering/model-core";

import datacatalog from "./plugin";
import { AnyComponent } from "@hcengineering/ui";
import { Ref, Doc, Domain } from "@hcengineering/core";

export const DOMAIN_DATACATALOG = 'datacatalog' as Domain

export const datacatalogId = 'datacatalog' as Plugin

export function createModel (builder: Builder): void {
  builder.createModel(TDatacatalogDoc)
  builder.createModel(TDataAssetsSpace)
  builder.createModel(TDomainsSpace)
  builder.createModel(TDataGovernanceSpace)

  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: datacatalog.string.Datacatalog,
      // locationDataResolver: datacatalog.function.LocationDataResolver,
      icon: datacatalog.icon.Datacatalog as Asset,
      alias: datacatalogId,
      hidden: false,
      navigatorModel: {
        specials: [
          {
            id: 'data-assets',
            label: datacatalog.string.DataAssets,
            icon: datacatalog.icon.Datacatalog,
            component: datacatalog.component.DataAssets,
            position: 'top'
          },
          {
            id: 'data-lineage',
            label: datacatalog.string.NavItem2,
            icon: datacatalog.icon.Datacatalog,
            component: datacatalog.component.DataLineage,
            position: 'top'
          },
          {
            id: 'observability',
            label: datacatalog.string.NavItem3,
            icon: datacatalog.icon.Datacatalog,
            component: datacatalog.component.Observability,
            position: 'top'
          },
          {
            id: 'domains',
            label: datacatalog.string.Domains,
            icon: datacatalog.icon.Datacatalog,
            component: datacatalog.component.Domains,
            position: 'top'
          },
          {
            id: 'glossary',
            label: datacatalog.string.Glossary,
            icon: datacatalog.icon.Datacatalog,
            component: datacatalog.component.Glossary,
            position: 'top'
          },
          {
            id: 'classification',
            label: datacatalog.string.Classification,
            icon: datacatalog.icon.Datacatalog,
            component: datacatalog.component.Classification,
            position: 'top'
          }
        ],
        spaces: []
      }
    },
    datacatalog.app.DatacatalogApp as Ref<Doc>
  )

}

export default datacatalog