//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

  import {IndexKind, Ref, Class } from '@hcengineering/core'
  import { DOMAIN_DATACATALOG } from '@hcengineering/datacatalog'
  import datacatalog from './plugin'
  import { DatacatalogDoc, DataAssetsSpace, DomainsSpace, DataGovernanceSpace } from '@hcengineering/datacatalog/src/types'
  import {
    Index,
    Model,
    Prop,
    TypeString,
    UX
  } from '@hcengineering/model'
  import core, { TDoc, TSpace } from '@hcengineering/model-core'
  import { IntlString, Asset } from '@hcengineering/platform'
  
  // Assuming pluginName could have hyphens, a safer import name might be needed if used directly as variable
  // For now, using pluginName as per the context provided for this file.
  import pluginDefaultExport from './plugin' // Changed import variable name
  
  export { datacatalogId } from './index'
  export { default } from './plugin'
  
  @Model(pluginDefaultExport.class.DatacatalogDoc as Ref<Class<DatacatalogDoc>>, core.class.Doc, DOMAIN_DATACATALOG)
  @UX(pluginDefaultExport.string.Datacatalog as IntlString, pluginDefaultExport.icon.Datacatalog as Asset)
  export class TDatacatalogDoc extends TDoc implements DatacatalogDoc {
    @Prop(TypeString(), pluginDefaultExport.string.Name)
    @Index(IndexKind.FullText)
      name!: string

    @Prop(TypeString(), pluginDefaultExport.string.Description)
    @Index(IndexKind.FullText)
      description?: string
  }

  @Model(pluginDefaultExport.class.DataAssetsSpace as Ref<Class<DataAssetsSpace>>, core.class.Space, DOMAIN_DATACATALOG)
  @UX(pluginDefaultExport.string.DataAssets as IntlString, pluginDefaultExport.icon.Datacatalog as Asset)
  export class TDataAssetsSpace extends TSpace implements DataAssetsSpace {
  }

  @Model(pluginDefaultExport.class.DomainsSpace as Ref<Class<DomainsSpace>>, core.class.Space, DOMAIN_DATACATALOG)
  @UX(pluginDefaultExport.string.Domains as IntlString, pluginDefaultExport.icon.Datacatalog as Asset)
  export class TDomainsSpace extends TSpace implements DomainsSpace {
  }

  @Model(pluginDefaultExport.class.DataGovernanceSpace as Ref<Class<DataGovernanceSpace>>, core.class.Space, DOMAIN_DATACATALOG)
  @UX(pluginDefaultExport.string.DataGovernance as IntlString, pluginDefaultExport.icon.Datacatalog as Asset)
  export class TDataGovernanceSpace extends TSpace implements DataGovernanceSpace {
  }