<script lang="ts">
    import { onDestroy } from 'svelte'
    import {
      deviceOptionsStore as deviceInfo,
      Location,
      location,
      restoreLocation,
      getCurrentLocation,
      navigate,
      Separator,
    } from '@hcengineering/ui'
    import { datacatalogId } from '@hcengineering/datacatalog'
    import { pathToComponentMap, sidebarItems } from '../constants'
    import Sidebar from './sidebar/Sidebar.svelte'
    import type { SvelteComponent } from 'svelte'

    type AppKey = keyof typeof pathToComponentMap
  
    let selectedApplication: AppKey | undefined = undefined
    let selectedCategory: string | undefined = undefined
    let selectedApplicationProps: any = {}
    let replacedPanel: HTMLElement
  
    let loc: Location | undefined = undefined
  
    const unsubscribe = location.subscribe((currLocation) => {
      loc = currLocation
    })
    onDestroy(() => {
      unsubscribe()
    })
  
    $: if (loc) syncLocation(loc)
  
    function syncLocation (loc: Location | undefined): void {
      if(!loc) {
        return
      }

      if (loc.path[2] !== datacatalogId) {
        return
      }
      
      if (loc.path[3]) {
        const mainPath = decodeURIComponent(loc.path[3])
        if (loc.path[4]) {
          // Handle sub-path (e.g., data-governance/glossary)
          const subPath = decodeURIComponent(loc.path[4])
          selectedApplication = `${mainPath}/${subPath}` as AppKey
        } else {
          // Check if this is a dropdown item that needs default sub-item
          const sidebarItem = sidebarItems.find((item: any) => item.path === mainPath)
          if (sidebarItem?.defaultSubItem) {
            // Redirect to default sub-item
            const newLoc = getCurrentLocation()
            newLoc.path[4] = encodeURIComponent(sidebarItem.defaultSubItem)
            navigate(newLoc)
            return
          }
          selectedApplication = mainPath as AppKey
        }
      } else {
        restoreLocation(loc, datacatalogId)
        return 
      }
    }

    function updateLocation(selectedApp?: string): void {
      if(!selectedApp) {
        return
      }

      const loc = getCurrentLocation()
      
      // Handle sub-paths
      if (selectedApp.includes('/')) {
        const [mainPath, subPath] = selectedApp.split('/')
        loc.path[3] = encodeURIComponent(mainPath)
        loc.path[4] = encodeURIComponent(subPath)
        loc.path.length = 5
      } else {
        loc.path[3] = encodeURIComponent(selectedApp)
        loc.path.length = 4
      }

      // Find the appropriate component props
      selectedApplicationProps = sidebarItems.find((item: any) => 
        item.path === selectedApp || 
        (item.subItems && item.subItems.find((sub: any) => sub.path === selectedApp))
      ) ?? {}

      navigate(loc)
    }
  
    $: updateLocation(selectedApplication)

    $: selectedApplicationProps = {
      ...(sidebarItems as any).find((item: any) => item.path === selectedApplication) ?? {},
      selectedCategory
    }

    // Determine component to render for current application
    let SelectedComponent: typeof SvelteComponent | null = null
    $: SelectedComponent = selectedApplication ? (pathToComponentMap[selectedApplication] as unknown as typeof SvelteComponent) : null
  
    $: $deviceInfo.replacedPanel = replacedPanel
    onDestroy(() => ($deviceInfo.replacedPanel = undefined))

    // Helper function to get component safely
    function getComponent(app: string) {
      return (pathToComponentMap as any)[app]
    }
  </script>
  
<div class="hulyPanels-container">
    {#if $deviceInfo.navigator.visible}
        <div
        class="antiPanel-navigator border-left"
        class:portrait={$deviceInfo.navigator.direction === 'horizontal'}
        class:landscape={$deviceInfo.navigator.direction !== 'horizontal'}
        class:fly={$deviceInfo.navigator.float}
        >
        <div class="antiPanel-wrap__content hulyNavPanel-container">
            <Sidebar bind:selectedApplication bind:selectedCategory />
        </div>
        {#if !($deviceInfo.isMobile && $deviceInfo.isPortrait && $deviceInfo.minWidth)}
            <Separator name="chat" float={$deviceInfo.navigator.float ? 'navigator' : true} index={0} />
        {/if}
        </div>
        <Separator
        name="chat"
        float={$deviceInfo.navigator.float}
        index={0}
        color={'transparent'}
        separatorSize={0}
        short
        />
    {/if}
    <div bind:this={replacedPanel} class="hulyComponent">
        {#if selectedApplication && getComponent(selectedApplication)}
          <svelte:component this={getComponent(selectedApplication)} {selectedCategory} />
        {/if}
    </div>
</div>