<script lang="ts">
  import { onMount } from 'svelte'
  import {
    EditBox,
    ListView,
    Button,
    ButtonIcon,
    Header,
    Spinner,
    Label,
    Scroller,
    NavItem,
    NavGroup,
    IconObjects,
    IconDetails,
    IconShare,
    IconThread,
    IconActivity,
    IconFile,
    IconSettings,
    IconFolder,
    IconLeft,
    Section,
    Grid
  } from '@hcengineering/ui'
  import type { AnySvelteComponent } from '@hcengineering/ui'
  import { getEmbeddedLabel } from '@hcengineering/platform'

  // Map OpenMetadata entity types to icons used in the navigation list
  const entityTypeIcons: Record<string, AnySvelteComponent> = {
    database: IconObjects,
    table: IconObjects,
    dashboard: IconDetails,
    pipeline: IconShare,
    topic: IconThread,
    chart: IconDetails,
    metric: IconActivity,
    report: IconFile,
    mlmodel: IconSettings,
    container: IconFolder
  }

  // OpenMetadata helpers
  import { search as omSearch, searchEntityType } from '../../openmetadata/search'
  import {
    getEntityByFqn,
    getEntityById,
    updateEntity,
    listEntities
  } from '../../openmetadata/entities'

  // ---------------------------------------------------------------------------
  // Types (minimal subset to keep the component typed yet flexible)
  // ---------------------------------------------------------------------------
  interface BasicRef {
    name?: string
    displayName?: string
  }

  interface AssetSearchHit {
    id?: string
    name?: string
    displayName?: string
    fullyQualifiedName: string
    entityType: string
    description?: string

    // Extra fields used for grouping / display
    service?: BasicRef
    database?: BasicRef
    databaseSchema?: BasicRef
  }

  interface Category {
    id: string
    label: string
    endpoint: string // OpenMetadata API endpoint
    icon?: AnySvelteComponent
    childLevels: { entityType: string; parentFilter: string | null }[]
  }

  interface Row {
    type: string
    label: string
    category: Category
    level: number
    expanded: boolean
    childrenLoaded: boolean
    entity?: AssetSearchHit
  }

  interface AssetDetail extends AssetSearchHit {
    owner?: any
    tags?: any[]
    columns?: Array<{ name: string; dataType: string }>
    usageSummary?: any
    charts?: any[]
    partitions?: number
    algorithm?: string
    target?: string
    mlFeatures?: Array<{
      name: string
      dataType?: string
      description?: string
      featureAlgorithm?: string
      featureSources?: any[]
    }>
    mlHyperParameters?: Array<{ name: string; value: string }>
    mlStore?: any
    server?: string
    numberOfObjects?: number
    size?: number
    tableConstraints?: any[]
    expression?: string
  }

  // ---------------------------------------------------------------------------
  // Component state
  // ---------------------------------------------------------------------------
  // Props that workbench navigation system will pass for special components
  import type { IntlString } from '@hcengineering/platform'

  export let title: IntlString | undefined = undefined
  export let icon: any = undefined

  // Internal state - no longer driven by external category prop
  export let selectedCategory: string | undefined = undefined

  // Intl placeholder/labels – temporary direct strings cast to any for IntlString compatibility
  const intlSearchAssets: any = getEmbeddedLabel('Search assets…')
  const intlNoAssets: any = getEmbeddedLabel('No assets found')
  const intlSelectAsset: any = getEmbeddedLabel('Select an asset to view details')
  const intlNextPage: any = getEmbeddedLabel('Next Page')
  const intlPrevPage: any = getEmbeddedLabel('Previous Page')
  const intlLoading: any = getEmbeddedLabel('Loading...')

  /**
   * Convert a plain string to the `IntlString` compatible type expected by
   * some UI components. This avoids the need for `as any` assertions directly
   * inside the Svelte markup, which are not allowed.
   */
  function toIntl (str: string): any {
    return getEmbeddedLabel(str)
  }

  $: if (selectedCategory) {
    viewCategory(selectedCategory)
  }

  const categories: Category[] = [
    {
      id: 'databases',
      label: 'Databases',
      endpoint: 'databases',
      icon: IconObjects,
      childLevels: [
        { entityType: 'databaseService', parentFilter: null },
        { entityType: 'database', parentFilter: 'service.fullyQualifiedName' },
        { entityType: 'databaseSchema', parentFilter: 'database.fullyQualifiedName' },
        { entityType: 'table', parentFilter: 'databaseSchema.fullyQualifiedName' }
      ]
    },
    {
      id: 'dashboards',
      label: 'Dashboards',
      endpoint: 'dashboards',
      icon: IconDetails,
      childLevels: [
        { entityType: 'dashboardService', parentFilter: null },
        { entityType: 'dashboard', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'pipelines',
      label: 'Pipelines',
      endpoint: 'pipelines',
      icon: IconShare,
      childLevels: [
        { entityType: 'pipelineService', parentFilter: null },
        { entityType: 'pipeline', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'topics',
      label: 'Topics',
      endpoint: 'topics',
      icon: IconThread,
      childLevels: [
        { entityType: 'messagingService', parentFilter: null },
        { entityType: 'topic', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'charts',
      label: 'Charts',
      endpoint: 'charts',
      icon: IconDetails,
      childLevels: [
        { entityType: 'chartService', parentFilter: null },
        { entityType: 'chart', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'metrics',
      label: 'Metrics',
      endpoint: 'metrics',
      icon: IconActivity,
      childLevels: [
        { entityType: 'metricService', parentFilter: null },
        { entityType: 'metric', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'reports',
      label: 'Reports',
      endpoint: 'reports',
      icon: IconFile,
      childLevels: [
        { entityType: 'reportService', parentFilter: null },
        { entityType: 'report', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'mlmodels',
      label: 'ML Models',
      endpoint: 'mlmodels',
      icon: IconSettings,
      childLevels: [
        { entityType: 'mlmodelService', parentFilter: null },
        { entityType: 'mlmodel', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'containers',
      label: 'Containers',
      endpoint: 'containers',
      icon: IconFolder,
      childLevels: [
        { entityType: 'storageService', parentFilter: null },
        { entityType: 'container', parentFilter: 'service.fullyQualifiedName' }
      ]
    }
  ]

  let searchTerm: string = ''
  let displayRows: Row[] = []
  let categoryEntities: Map<string, AssetSearchHit[]> = new Map()
  let assetsLoading = false

  let selectedFqn: string | null = null
  let selectedAsset: AssetDetail | null = null
  let detailsLoading = false

  // description editing
  let editingDescription = false
  let descriptionDraft: string = ''

  // pagination
  let limit = 20
  let offset = 0
  let total = 0
  let currentPage = 1
  let paginationLoading = false

  // ---------------------------------------------------------------------------
  // Helper functions
  // ---------------------------------------------------------------------------
  async function loadAssets (): Promise<void> {
    assetsLoading = true
    try {
      const trimmedTerm = searchTerm.trim()
      if (!trimmedTerm) {
        // Reset search results
        displayRows = []
        selectedFqn = null
        selectedAsset = null
      } else {
        // Search results view
        const q = trimmedTerm
        const searchParams: import('../../openmetadata/search').SearchParams = {
          q,
          size: limit,
          from: offset,
          sortField: 'name',
          sortOrder: 'asc'
        }
        const res = await omSearch<AssetSearchHit>(searchParams)
        total = res.paging?.total || 0

        // Create a flat list of assets, sorted by name, instead of grouping by service
        const rows: Row[] = res.data
          .sort((a, b) => {
            const aName = (a.displayName ?? a.name ?? a.fullyQualifiedName).toLowerCase()
            const bName = (b.displayName ?? b.name ?? b.fullyQualifiedName).toLowerCase()
            return aName.localeCompare(bName)
          })
          .map((hit) => {
            const label = hit.displayName ?? hit.name ?? hit.fullyQualifiedName
            const serviceName = hit.service?.displayName ?? hit.service?.name
            return {
              type: hit.entityType ?? 'item',
              label: serviceName ? `${serviceName} / ${label}` : label,
              entity: hit,
              // All items are at the root level, not expandable by default in search
              category:
                categories.find((c) =>
                  c.childLevels.some((l) => l.entityType === hit.entityType)
                ) ?? categories[0],
              level: 0,
              expanded: false,
              childrenLoaded: false
            }
          })

        displayRows = rows
        selectedFqn = null
        selectedAsset = null
      }
    } catch (err) {
      console.error('Failed to fetch assets', err)
    } finally {
      assetsLoading = false
    }
  }

  async function loadPaginatedList (categoryId: string): Promise<void> {
    paginationLoading = true
    try {
      const category = categories.find(c => c.id === categoryId)
      if (!category) return
      
      const endpoint = category.endpoint
      
      const params = {
        limit,
        offset
      }
      
      const response = await listEntities(endpoint, params)
      total = response.paging?.total || 0
      
      const assets = response.data.map((entity: any) => ({
        ...entity,
        entityType: entity.entityType ?? category.childLevels[1].entityType,
        fullyQualifiedName: entity.fullyQualifiedName
      }))
      
      categoryEntities.set(categoryId, assets)
      categoryEntities = categoryEntities
    } catch (err) {
      console.error(`Failed to fetch ${categoryId}`, err)
    } finally {
      paginationLoading = false
    }
  }

  async function fetchAssetDetails (asset: AssetSearchHit): Promise<void> {
    selectedAsset = null
    detailsLoading = true
    try {
      const type = asset.entityType.toLowerCase()
      const endpoint = type.endsWith('s') ? type : `${type}s`
      const fields = [...commonFields, ...(extraFields[type] ?? [])]

      const detail = await safeGetEntity<AssetDetail>(
        endpoint,
        { fqn: asset.fullyQualifiedName, id: asset.id },
        fields
      )

      if (detail) selectedAsset = { ...asset, ...detail }
    } catch (err) {
      console.error('Failed to fetch asset details', err)
    } finally {
      detailsLoading = false
    }
  }

  async function saveDescription (): Promise<void> {
    if (!selectedAsset || !selectedAsset.id) return

    try {
      await updateEntity(
        selectedAsset.entityType.toLowerCase(),
        selectedAsset.id,
        {
          ...selectedAsset,
          description: descriptionDraft
        }
      )
      selectedAsset = { ...selectedAsset, description: descriptionDraft }
      editingDescription = false
    } catch (err) {
      console.error('Failed to update asset description', err)
    }
  }

  // debounce search input
  let searchHandle: number | null = null
  function onSearchInput (): void {
    if (searchHandle) clearTimeout(searchHandle)
    searchHandle = window.setTimeout(() => {
      offset = 0
      currentPage = 1
      loadAssets()
    }, 400)
  }

  /** Return readable label for asset, prefixed with service name if available */
  function assetLabel (asset: AssetSearchHit): string {
    const name = asset.displayName ?? asset.name ?? asset.fullyQualifiedName
    const serviceName = asset.service?.displayName ?? asset.service?.name
    return serviceName ? `${serviceName} / ${name}` : name
  }

  function groupByService (list: AssetSearchHit[]): Map<string, AssetSearchHit[]> {
    const m = new Map<string, AssetSearchHit[]>()
    for (const a of list) {
      const svc = a.service?.displayName ?? a.service?.name ?? 'Unknown Service'
      const arr = m.get(svc) ?? []
      arr.push(a)
      m.set(svc, arr)
    }
    return m
  }

  function nextPage(): void {
    if (offset + limit < total) {
      offset += limit
      currentPage++
      if (searchTerm.trim() !== '') {
        loadAssets()
      } else if (selectedCategory) {
        loadPaginatedList(selectedCategory)
      }
    }
  }

  function prevPage(): void {
    if (offset > 0) {
      offset = Math.max(0, offset - limit)
      currentPage--
      if (searchTerm.trim() !== '') {
        loadAssets()
      } else if (selectedCategory) {
        loadPaginatedList(selectedCategory)
      }
    }
  }

  async function viewCategory(categoryId: string): Promise<void> {
    offset = 0
    currentPage = 1
    categoryEntities.clear()
    await loadPaginatedList(categoryId)
  }

  function handleCategoryClick(category: Category): void {
    viewCategory(category.id)
  }

  function handleBackToList (): void {
    selectedAsset = null
    selectedFqn = null
  }

  function handleEntityClick(asset: AssetSearchHit): void {
    selectedFqn = asset.fullyQualifiedName
    fetchAssetDetails(asset)
  }

  function getCategoryLabel(categoryId: string): any {
    return categories.find(c => c.id === categoryId)?.label || ''
  }

  onMount(() => {
    // Initial load - just show category list
  })

  // ---------------------------------------------------------------------------
  // Generic helpers
  // ---------------------------------------------------------------------------

  const commonFields = ['tags', 'usageSummary']
  const extraFields: Record<string, string[]> = {
    table: ['columns', 'tableConstraints'],
    dashboard: ['charts'],
    topic: ['partitions'],
    mlmodel: ['algorithm', 'target', 'mlFeatures', 'mlHyperParameters', 'mlStore', 'server'],
    container: ['numberOfObjects', 'size'],
    metric: ['expression']
  }

  async function safeGetEntity<T> (
    endpoint: string,
    fqnOrId: { fqn?: string; id?: string },
    fields: string[]
  ): Promise<T> {
    try {
      return fqnOrId.fqn
        ? await getEntityByFqn<T>(endpoint, fqnOrId.fqn, fields)
        : await getEntityById<T>(endpoint, fqnOrId.id!, fields)
    } catch (err: any) {
      // Retry without fields on 4xx errors (invalid field etc.)
      const msg = String(err?.message ?? '')
      if (err?.code === 400 || err?.status === 400 || msg.includes('Invalid') || msg.includes('status: 400')) {
        return fqnOrId.fqn
          ? await getEntityByFqn<T>(endpoint, fqnOrId.fqn)
          : await getEntityById<T>(endpoint, fqnOrId.id!)
      }
      throw err
    }
  }

  // ---------------- Generic extra section ----------------------
  const KNOWN_PROPS = new Set([
    'id', 'name', 'displayName', 'fullyQualifiedName', 'entityType',
    'description', 'owner', 'tags', 'columns', 'usageSummary', 'charts',
    'partitions', 'algorithm', 'target', 'mlFeatures', 'mlHyperParameters',
    'mlStore', 'server', 'numberOfObjects', 'size', 'tableConstraints',
    'expression', 'service'
  ])
  let extraEntries: Array<[string, any]> = []
  $: extraEntries = selectedAsset ? Object.entries(selectedAsset).filter(([k]) => !KNOWN_PROPS.has(k)) : []
</script>

<style lang="scss">
  .data-assets-container {
    display: flex;
    flex-direction: column;
    min-height: 0;
    height: 100%;
  }
  .list-view-container {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .details-view-container {
    flex: 1 1 auto;
    overflow: auto;
    padding: 1rem;
  }
  /* Removed standalone search-bar – search now integrated into Header */
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-top: 1px solid var(--theme-popup-divider);
  }
  .asset-detail-section {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--theme-popup-divider);
  }
  .asset-tag {
    display: inline-block;
    background: var(--theme-button-secondary-enabled);
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    margin: 0.2rem;
    font-size: 0.9em;
  }
  .section-header {
    font-weight: bold;
    margin-bottom: 0.5rem;
  }
  /* ---------------- New styling ---------------- */
  .asset-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    gap: 1rem;
  }
  .asset-detail-header .title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  .asset-icon {
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
  }
  .asset-details-grid {
    display: grid;
    grid-template-columns: 120px 1fr;
    row-gap: 0.25rem;
    column-gap: 0.75rem;
    word-break: break-all;
  }
  .detail-label {
    font-weight: bold;
  }

  /* Search results list item */
  .list-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
  }
  .list-item-icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }
</style>

<div class="data-assets-container">
  {#if selectedAsset}
    <div class="details-view-container">
      <!-- Unified Huly header for details view -->
      <Header type="type-aside" hideDescription hideSearch hideActions allowFullsize={false}>
        <ButtonIcon
          slot="beforeTitle"
          icon={IconLeft}
          kind="tertiary"
          size="small"
          on:click={handleBackToList}
        />

        <div class="title">
          <svelte:component this={entityTypeIcons[selectedAsset.entityType ?? ''] ?? IconFile} class="asset-icon" />
          {selectedAsset.displayName ?? selectedAsset.name}
        </div>
      </Header>

      <!-- Key meta information -->
      <Section label={toIntl('Metadata')}>
        <svelte:fragment slot="content">
          <Grid column={2} rowGap={0.25} columnGap={0.75}>
            <span class="detail-label">Type</span>
            <span>{selectedAsset.entityType}</span>

            <span class="detail-label">FQN</span>
            <code class="truncate">{selectedAsset.fullyQualifiedName}</code>
          </Grid>
        </svelte:fragment>
      </Section>

      <!-- Description Editing -->
      <Section label={toIntl('Description')}>
        <svelte:fragment slot="content">
          {#if editingDescription}
            <EditBox format="text-multiline" bind:value={descriptionDraft} fullSize />
            <div class="flex-row" style="margin-top:0.5rem; gap:0.5rem;">
              <Button on:click={saveDescription}>Save</Button>
              <Button on:click={() => (editingDescription = false)}>Cancel</Button>
            </div>
          {:else}
            <p>{selectedAsset.description ?? '-'}</p>
            <Button size="small" on:click={() => { descriptionDraft = selectedAsset?.description ?? ''; editingDescription = true }}>Edit</Button>
          {/if}
        </svelte:fragment>
      </Section>

      {#if selectedAsset.owner}
      <div class="asset-detail-section">
        <div class="section-header">Owner</div>
        <div>
          {selectedAsset.owner.displayName ?? selectedAsset.owner.name ?? selectedAsset.owner.id}
        </div>
      </div>
      {/if}

      {#if selectedAsset.columns && selectedAsset.columns.length}
      <div class="asset-detail-section">
        <div class="section-header">Schema</div>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th style="text-align: left; padding: 0.5rem;">Name</th>
              <th style="text-align: left; padding: 0.5rem;">Data Type</th>
            </tr>
          </thead>
          <tbody>
            {#each selectedAsset.columns as col}
              <tr>
                <td style="padding: 0.5rem;">{col.name}</td>
                <td style="padding: 0.5rem;">{col.dataType}</td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
      {/if}

      {#if selectedAsset.tags && selectedAsset.tags.length}
      <div class="asset-detail-section">
        <div class="section-header">Tags</div>
        <div>
          {#each selectedAsset.tags as tag}
            <span class="asset-tag">{tag.tagFQN ?? tag.fullyQualifiedName ?? tag.name}</span>
          {/each}
        </div>
      </div>
      {/if}

      {#if selectedAsset.charts && selectedAsset.charts.length}
      <div class="asset-detail-section">
        <div class="section-header">Charts</div>
        <ul>
          {#each selectedAsset.charts as chart}
            <li>{chart.displayName ?? chart.name}</li>
          {/each}
        </ul>
      </div>
      {/if}

      {#if selectedAsset.partitions !== undefined}
      <div class="asset-detail-section">
        <div class="section-header">Partitions</div>
        <div>{selectedAsset.partitions}</div>
      </div>
      {/if}

      {#if selectedAsset.algorithm}
      <div class="asset-detail-section">
        <div class="section-header">ML Model</div>
        <div><b>Algorithm:</b> {selectedAsset.algorithm}</div>
        {#if selectedAsset.target}
          <div><b>Target:</b> {selectedAsset.target}</div>
        {/if}
      </div>
      {/if}

      {#if selectedAsset.mlFeatures && selectedAsset.mlFeatures.length}
      <div class="asset-detail-section">
        <div class="section-header">Features</div>
        <ul>
          {#each selectedAsset.mlFeatures as feat}
            <li>
              <b>{feat.name}</b> ({feat.dataType})
              {#if feat.description}
                – {feat.description}
              {/if}
              {#if feat.featureAlgorithm}
                [Algorithm: {feat.featureAlgorithm}]
              {/if}
            </li>
          {/each}
        </ul>
      </div>
      {/if}

      {#if selectedAsset.mlHyperParameters && selectedAsset.mlHyperParameters.length}
      <div class="asset-detail-section">
        <div class="section-header">Hyper-parameters</div>
        <ul>
          {#each selectedAsset.mlHyperParameters as hp}
            <li>{hp.name}: {hp.value}</li>
          {/each}
        </ul>
      </div>
      {/if}

      {#if selectedAsset.mlStore}
      <div class="asset-detail-section">
        <div class="section-header">Model Store</div>
        <pre>{JSON.stringify(selectedAsset.mlStore, null, 2)}</pre>
      </div>
      {/if}

      {#if selectedAsset.server}
      <div class="asset-detail-section">
        <div class="section-header">Server</div>
        <div>{selectedAsset.server}</div>
      </div>
      {/if}

      {#if selectedAsset.numberOfObjects !== undefined}
      <div class="asset-detail-section">
        <div class="section-header">Container Stats</div>
        <div><b>Objects:</b> {selectedAsset.numberOfObjects}</div>
        {#if selectedAsset.size !== undefined}
          <div><b>Size:</b> {selectedAsset.size} bytes</div>
        {/if}
      </div>
      {/if}

      {#if selectedAsset.expression}
      <div class="asset-detail-section">
        <div class="section-header">Metric Expression</div>
        <code>{selectedAsset.expression}</code>
      </div>
      {/if}

      {#if selectedAsset.tableConstraints && selectedAsset.tableConstraints.length}
      <div class="asset-detail-section">
        <div class="section-header">Constraints</div>
        <ul>
          {#each selectedAsset.tableConstraints as constraint}
            <li>{constraint.name}: {constraint.constraintType}</li>
          {/each}
        </ul>
      </div>
      {/if}

      {#if selectedAsset.usageSummary}
      <div class="asset-detail-section">
        <div class="section-header">Usage Summary</div>
        <div>
          {#if selectedAsset.usageSummary.dailyStats}
            <div><b>Daily Views:</b> {selectedAsset.usageSummary.dailyStats.count ?? 0}</div>
          {/if}
          {#if selectedAsset.usageSummary.weeklyStats}
            <div><b>Weekly Views:</b> {selectedAsset.usageSummary.weeklyStats.count ?? 0}</div>
          {/if}
          {#if selectedAsset.usageSummary.monthlyStats}
            <div><b>Monthly Views:</b> {selectedAsset.usageSummary.monthlyStats.count ?? 0}</div>
          {/if}
        </div>
      </div>
      {/if}

      {#if extraEntries.length}
      <div class="asset-detail-section">
        <div class="section-header">Additional Properties</div>
        <ul>
          {#each extraEntries as [k, v]}
            <li><b>{k}</b>: {JSON.stringify(v)}</li>
          {/each}
        </ul>
      </div>
      {/if}

    </div>
  {:else}
    <div class="list-view-container">
      <!-- Top header with integrated search -->
      <Header hideDescription hideActions>
        Data Assets

        <EditBox
          slot="search"
          placeholder={intlSearchAssets}
          bind:value={searchTerm}
          on:input={onSearchInput}
          fullSize
        />
      </Header>

      <Scroller>
        {#if assetsLoading || paginationLoading}
          <div class="flex-row-center" style="padding: 1rem;">
            <Spinner size="medium" />
          </div>
        {:else if searchTerm.trim() && displayRows.length === 0}
          <div style="padding: 1rem;">
            <Label label={intlNoAssets} />
          </div>
        {:else if searchTerm.trim() && displayRows.length > 0}
          <!-- Search Results -->
          <ListView
            items={displayRows}
            count={displayRows.length}
            getKey={(i) => `${displayRows[i].level}-${displayRows[i].label}-${displayRows[i].entity?.fullyQualifiedName ?? ''}`}
            on:click={(e) => {
              const idx = e.detail
              const row = displayRows[idx]
              if (row.entity) {
                selectedFqn = row.entity.fullyQualifiedName
                fetchAssetDetails(row.entity)
              }
            }}
          >
            <svelte:fragment slot="item" let:item={idx}>
              <div
                class="list-item"
                class:selected={displayRows[idx].entity?.fullyQualifiedName === selectedFqn}
              >
                <svelte:component
                  this={entityTypeIcons[displayRows[idx].entity?.entityType ?? ''] ?? IconFile}
                  class="list-item-icon"
                />
                <span class="truncate">{displayRows[idx].label}</span>
              </div>
            </svelte:fragment>
          </ListView>
        {:else}
          <!-- Show assets when a category is selected -->
          {#if categoryEntities.size > 0}
            {#each Array.from(categoryEntities.entries()) as [categoryId, assets]}
              {@const category = categories.find(c => c.id === categoryId)}
              {#if category}
              <NavGroup categoryName={category.id} label={toIntl(category.label)} icon={category.icon} isOpen={true} noDivider>
                {#each Array.from(groupByService(assets).entries()) as [svc, svcAssets]}
                  <NavGroup
                    categoryName={svc}
                    label={toIntl(svc)}
                    folderIcon
                    isFold
                    noDivider
                  >
                    {#each svcAssets as asset}
                      <NavItem
                        label={toIntl(asset.displayName ?? asset.name ?? asset.fullyQualifiedName)}
                        icon={entityTypeIcons[asset.entityType ?? ''] ?? IconFile}
                        withBackground={false}
                        selected={selectedFqn === asset.fullyQualifiedName}
                        on:click={() => handleEntityClick(asset)}
                      >
                        <span slot="extra" class="font-regular-12 caption">{asset.entityType}</span>
                      </NavItem>
                    {/each}
                  </NavGroup>
                {/each}
              </NavGroup>
              {/if}
            {/each}
          {/if}
        {/if}
      </Scroller>
      
      <!-- Pagination -->
      {#if (searchTerm.trim() !== '' || categoryEntities.size > 0) && total > limit}
        <div class="pagination">
          <Button size="small" disabled={offset === 0} on:click={prevPage}>
            {intlPrevPage}
          </Button>
          <span>{currentPage} / {Math.ceil(total / limit)}</span>
          <Button size="small" disabled={offset + limit >= total} on:click={nextPage}>
            {intlNextPage}
          </Button>
        </div>
      {/if}
    </div>
  {/if}
</div>