<script lang="ts">
    import { Label } from '@hcengineering/ui'
    import type { IntlString } from '@hcengineering/platform'
    import plugin from '../../plugin'

    // Props that workbench navigation system will pass for special components
    export let title: IntlString = plugin.string.Domains
    export let icon: any = undefined
</script>

<div>
    <Label label={title} />
    <p>Domains functionality will be implemented here.</p>
</div>