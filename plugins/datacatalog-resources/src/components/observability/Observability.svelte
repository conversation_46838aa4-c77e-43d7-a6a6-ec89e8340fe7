<script lang="ts">
    import { Label } from '@hcengineering/ui'
    import type { IntlString } from '@hcengineering/platform'
    import plugin from '../../plugin'

    // Props that workbench navigation system will pass
    export let title: IntlString = plugin.string.NavItem3
    export let icon: any = undefined
</script>

<div>
    <Label label={title} />
    <p>Observability functionality will be implemented here.</p>
</div>