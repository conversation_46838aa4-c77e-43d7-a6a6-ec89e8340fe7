<script lang="ts">
    import { Scroller, NavItem, NavGroup, Label, Icon } from '@hcengineering/ui'
    import { NavFooter } from '@hcengineering/workbench-resources'
    import datacatalog from '../../plugin'
    import { sidebarItems, assetCategories } from '../../constants'
    import { getEmbeddedLabel } from '@hcengineering/platform'

    export let selectedApplication: string | undefined = undefined
    export let selectedCategory: string | undefined = undefined

    function handleNavItemClick(sidebarItem: any): void {
      if (sidebarItem.defaultSubItem) {
        // Auto-select default sub-item for dropdown
        selectedApplication = `${sidebarItem.path}/${sidebarItem.defaultSubItem}`
        // Reset category selection when navigating to dropdown view
        selectedCategory = undefined
      } else {
        selectedApplication = sidebarItem.path
        if (sidebarItem.path === 'data-assets') {
          // Default to first asset category if none selected
          selectedCategory = assetCategories[0]?.id
        } else {
          selectedCategory = undefined
        }
      }
    }

    function handleSubItemClick(subItem: any): void {
      selectedApplication = subItem.path
      selectedCategory = undefined
    }

    // Make these reactive by using $: statements
    $: isDropdownSelected = (item: any): boolean => {
      if (!selectedApplication) return false
      return selectedApplication.startsWith(item.path + '/') || selectedApplication === item.path
    }

    $: isSubItemSelected = (subItem: any): boolean => {
      return selectedApplication === subItem.path
    }
</script>
  
<div class="hulyNavPanel-header">
    <span class="overflow-label">
        <Label label={datacatalog.string.ApplicationTitle} />
    </span>
</div>
  
<Scroller shrink>
  {#each sidebarItems as sidebarItem (sidebarItem.id)}
    {#if sidebarItem.isDropdown}
      <NavGroup
        label={sidebarItem.title}
        icon={sidebarItem.icon}
        categoryName={sidebarItem.id}
        highlighted={isDropdownSelected(sidebarItem)}
        isFold
        isOpen={isDropdownSelected(sidebarItem)}
        noDivider
      >
        {#each sidebarItem.subItems as subItem (subItem.id)} 
          <NavItem
            label={subItem.title}
            icon={subItem.icon}
            selected={isSubItemSelected(subItem)}
            on:click={() => handleSubItemClick(subItem)}
          />
        {/each}
      </NavGroup>
    {:else}
      <NavItem
        label={sidebarItem.title}
        icon={sidebarItem.icon}
        selected={selectedApplication === sidebarItem.path}
        on:click={() => handleNavItemClick(sidebarItem)}
      />
    {/if}
  {/each}

  {#if selectedApplication === 'data-assets'}
    {@const da = sidebarItems.find(i => i.path === 'data-assets')}
    <NavGroup
      label={da?.title ?? getEmbeddedLabel('Data Assets')}
      icon={da?.icon}
      categoryName="da-sub"
      isOpen
      noDivider
    >
      {#each assetCategories as category (category.id)}
        <NavItem
          label={category.label}
          icon={category.icon}
          selected={selectedCategory === category.id}
          on:click={() => { selectedCategory = category.id }}
        />
      {/each}
    </NavGroup>
  {/if}

  {#if selectedApplication && selectedApplication.startsWith('data-governance')}
    {@const dg = sidebarItems.find(i => i.path === 'data-governance')}
    {#if dg && dg.subItems}
      <NavGroup
        label={dg.title}
        icon={dg.icon}
        categoryName="dg-sub"
        isOpen
        noDivider
      >
        {#each dg.subItems as sub (sub.id)}
          <NavItem
            label={sub.title}
            icon={sub.icon}
            selected={selectedApplication === sub.path}
            on:click={() => handleSubItemClick(sub)}
          />
        {/each}
      </NavGroup>
    {/if}
  {/if}
</Scroller>
<NavFooter />

<style lang="scss">
.hulyNavPanel-header {
  display: flex;
  align-items: center;
  padding: var(--space-medium);
  gap: var(--space-small);

  .overflow-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
  }
}
</style>
