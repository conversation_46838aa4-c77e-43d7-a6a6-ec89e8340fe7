import { mergeIds } from '@hcengineering/platform'
import datacatalog, {datacatalogId} from '@hcengineering/datacatalog'
import type { IntlString } from '@hcengineering/platform'
import type { AnyComponent } from '@hcengineering/ui'

// Import components
import DataAssets from './components/data-assets/DataAssets.svelte'
import DataLineage from './components/data-lineage/DataLineage.svelte'
import Observability from './components/observability/Observability.svelte'
import Domains from './components/domains/Domains.svelte'
import Glossary from './components/data-governance/Glossary.svelte'
import Classification from './components/data-governance/Classification.svelte'

export default mergeIds(datacatalogId, datacatalog, {
    string: {
        ApplicationTitle: '' as IntlString,
        NavItem1: '' as IntlString,
        NavItem2: '' as IntlString,
        NavItem3: '' as IntlString,
        NavItem4: '' as IntlString,
        NavItem5: '' as IntlString,
        Glossary: '' as IntlString,
        Classification: '' as IntlString,
        SearchGlossary: '' as IntlString,
        SearchClassification: '' as IntlString,
        Filter: '' as IntlString,
        Tag: '' as IntlString,
        TermForTable: '' as IntlString,
        DescriptionForTable: '' as IntlString,
        OwnersForTable: '' as IntlString,
        TagsForTable: '' as IntlString,
        DomainsForTable: '' as IntlString,
        Back: '' as IntlString,
        AllTags: '' as IntlString,
        Status: '' as IntlString,
        Active: '' as IntlString,
        Disabled: '' as IntlString,
        Approved: '' as IntlString,
        FullyQualifiedName: '' as IntlString,
        UsageCount: '' as IntlString,
        ChildrenCount: '' as IntlString,
        Owners: '' as IntlString,
        Reviewers: '' as IntlString,
        Tags: '' as IntlString,
        TotalTags: '' as IntlString,
        Terms: '' as IntlString,
        All: '' as IntlString,
        NoDescriptionAvailable: '' as IntlString,
        DataAssets: '' as IntlString,
        Domains: '' as IntlString,
        DataGovernance: '' as IntlString,
        Tables: '' as IntlString,
        Dashboards: '' as IntlString,
        Pipelines: '' as IntlString,
        Containers: '' as IntlString,
    },
    component: {
        DataAssets: DataAssets as unknown as AnyComponent,
        DataLineage: DataLineage as unknown as AnyComponent,
        Observability: Observability as unknown as AnyComponent,
        Domains: Domains as unknown as AnyComponent,
        Glossary: Glossary as unknown as AnyComponent,
        Classification: Classification as unknown as AnyComponent,
    }
})